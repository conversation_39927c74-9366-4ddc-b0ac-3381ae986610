{#
 * This file is part of the Recommend Product plugin
 *
 * Copyright (C) EC-CUBE CO.,LTD. All Rights Reserved.
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
#}

<!-- CART RECOMMEND -->
{% if products|length %}
    <!-- List item -->
    {% if from_page == 'cart' %}
        <div class="mx-auto w-full max-w-[960px]">
            {{ include('/Product/_part_product_cart.twig', {products: products}) }}
        </div>
    {% else %}
        <p class="w-fit text-[15px] md:text-[16px] text-[#333333] mt-4">一緒に購入されている商品</p>
        <div class="mx-auto w-full max-w-[960px]">
            <!-- List item -->
            <ul class="flex flex-wrap gap-x-[4%] max-w-[960px] m-auto mt-[5px]">
                {% for Product in products %}
                    <li class="relative max-w-[48%] md:max-w-[22%] py-3 {% if loop.index > 10 %}hidden md:block{% endif %}">
                        {{ include('/Product/_part_product.twig', {Product: Product}) }}
                    </li>
                {% endfor %}
            </ul>
        </div>
    {% endif %}
{% endif %}
