{#
 * This file is part of the Recommend Product plugin
 *
 * Copyright (C) EC-CUBE CO.,LTD. All Rights Reserved.
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
#}

<!-- CART RECOMMEND -->
{% if products_later|length %}
    <div class="max-w-[960px] w-full mx-auto pb-10">
        <div class="bg-[#bababa] h-[1px]"></div>
    </div>
    <p class="w-fit text-[15px] md:text-[16px] text-[#333333] mt-4">あとで買う</p>
    <div class="mx-auto w-full max-w-[960px] pb-10">
        <ul class="max-w-[960px] m-auto mt-[5px]">
            {% for LaterProduct in products_later|slice(0, 8) %}
                {% set Product = LaterProduct.Product %}
                {% set form = forms[LaterProduct.Product.id] %}
                <li class="w-full flex py-3">
                    {{ include('/Product/_part_product_horizontal.twig', {Product: Product, form: form }) }}
                </li>
            {% endfor %}
        </ul>
    </div>
{% endif %}

{% if products_favorite|length %}
    <div class="max-w-[960px] w-full mx-auto pb-10">
        <div class="bg-[#bababa] h-[1px]"></div>
    </div>
    <p class="w-fit text-[15px] md:text-[16px] text-[#333333] mt-4">お気に入り</p>
    <div class="mx-auto w-full max-w-[960px] pb-10">
        <ul class="max-w-[960px] m-auto mt-[5px]">
            {% for FavoriteProduct in products_favorite|slice(0, 4) %}
                {% set Product = FavoriteProduct.Product %}
                {% set form = forms[LaterProduct.Product.id] %}
                <pre>
                    {{ dump(form) }}
                </pre>
                <li class="w-full flex py-3">
                    {{ include('/Product/_part_product_horizontal.twig', {Product: Product, form: form }) }}
                </li>
            {% endfor %}
        </ul>
    </div>
{% endif %}
