{#
 * This file is part of the Recommend Product plugin
 *
 * Copyright (C) EC-CUBE CO.,LTD. All Rights Reserved.
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
#}

{% block javascript %}
    <script>
        $('.slick_recommend').each(function () {
            $(this).slick({
                infinite: true,
                slidesToShow: 4,
                swipeToSlide: true,
                arrows: true,
                prevArrow: $(this).closest('div').find('#recslide_prev'),
                nextArrow: $(this).closest('div').find('#recslide_next'),
                responsive: [
                    {
                        breakpoint: 960,
                        settings: {
                            slidesToShow: 3,
                            swipeToSlide: true,
                        }
                    },
                    {
                        breakpoint: 640,
                        settings: {
                            slidesToShow: 2,
                            swipeToSlide: false,
                            slidesToScroll: 2,
                        }
                    }
                ]
            });
        });
    </script>
{% endblock %}


{% set isNew = false %}
{% set isSale = false %}

<!-- CART RECOMMEND -->
<div class="mx-auto my-[25px] md:my-[50px]">
    <p class="w-fit text-[15px] md:text-[16px] text-[#333333] mt-4">一緒に購入されている商品</p>

    <div class="relative max-w-[920px] md:justify-center m-auto pt-[25px]">
        <button id="recslide_prev" type="button" class="hidden md:block absolute top-0 -left-12 flex items-center justify-center h-full cursor-pointer hover:opacity-50 slick-arrow" data-carousel-prev="" style="">
            <img class="h-[25px] md:h-[41px]" src="{{ asset('assets/img/common/btn-prev.svg') }}" loading="lazy">
        </button>
        <button id="recslide_next" type="button" class="hidden md:block absolute top-0 -right-12 flex items-center justify-center h-full cursor-pointer hover:opacity-50 slick-arrow" data-carousel-next="" style="">
            <img class="h-[25px] md:h-[41px]" src="{{ asset('assets/img/common/btn-next.svg') }}" loading="lazy">
        </button>

            <!-- List item -->
        <ul class="slick_recommend slick-product w-[105.5%] md:w-full z-0 flex">
        {% for Product in products %}
            <li class="relative pt-[15px]">
                <a href="{{ url('product_detail', {'id': Product.id}) }}" class="hover:opacity-50 pr-[15px] md:px-5 block justify-center items-center">
                    {% set image = asset(Product.mainFileName|no_image_product, 'save_image') %}
                    <img class="object-cover rounded-2xl w-full aspect-square"
                        src="{{ is_mobile() ? image | imagine_filter('resize') : image }}" alt="{{ Product.name }}" width="202" height="202">
                    {% if Product.Tags is not empty %}
                        <div class="flex gap-x-4 gap-y-1 flex-wrap mt-2">
                            {% for Tag in Product.Tags|sort((a, b) => a.sort_no <=> b.sort_no) %}
                                {% if Tag.id == 1 %}
                                    {% set isNew = true %}
                                {% elseif Tag.id == 4 %}
                                    {% set isSale = true %}
                                {% else %}
                                    <p class="font-semibold tracking-[.06em] text-[#0089ff] text-[10px] md:text-[12px]">{{ Tag.name }}</p>
                                {% endif %}
                            {% endfor %}
                        </div>

                        {% if isNew %}
                                <div class="absolute md:left-[-5px] -left-3 top-3 md:top-2 bg-[#fee44d] flex items-center justify-center rounded-full w-[34px] h-[34px] md:w-[41px] md:h-[41px]">
                                <p class="font-light text-[13px] md:text-[15px] din-condensed">NEW</p>
                            </div>
                        {% endif %}
                    {% endif %}
                    <p class="pt-2 text-[13px] md:text-[14px] break-all">{{ Product.name }}</p>
                    {% if Product.getPrice01IncTaxMin is not null %}
                        <p class="pt-[15px] md:pt-[14px]">
                            <span class="line-through text-[12px] md:text-[16px]">
                                <span>
                                    {% if Product.hasProductClass -%}
                                        {% if Product.getPrice01IncTaxMin == Product.getPrice01IncTaxMax %}
                                            {{ Product.getPrice01IncTaxMin|number_format(0, '.', ',') }}
                                        {% else %}
                                            {{ Product.getPrice01IncTaxMin|number_format(0, '.', ',') }}
                                            <span class="text-[10px] md:text-[12px]">円 ～</span>
                                            {{ Product.getPrice01IncTaxMax|number_format(0, '.', ',') }}
                                        {% endif %}
                                    {% else %}
                                        {{ Product.getPrice01IncTaxMin|number_format(0, '.', ',') }}
                                    {% endif %}
                                </span>
                                <span class="text-[10px] md:text-[12px]">円</span>
                            </span>
                            <span class="line-through text-[8px] md:text-[12px]"> (税込)</span>
                        </p>
                        <p class="text-[red]">
                            <span class="text-[14px] md:text-[18px]">
                                <span>
                                    {% if Product.hasProductClass -%}
                                        {% if Product.getPrice02IncTaxMin == Product.getPrice02IncTaxMax %}
                                            {{ Product.getPrice02IncTaxMin|number_format(0, '.', ',') }}
                                        {% else %}
                                            {{ Product.getPrice02IncTaxMin|number_format(0, '.', ',') }}
                                            <span class="text-[12px] md:text-[14px]">円 ～</span>
                                            {{ Product.getPrice02IncTaxMax|number_format(0, '.', ',') }}
                                        {% endif %}
                                    {% else %}
                                        {{ Product.getPrice02IncTaxMin|number_format(0, '.', ',') }}
                                    {% endif %}
                                </span>
                                <span class="text-[12px] md:text-[14px]">円</span>
                            </span>
                            <span class="text-[10px] md:text-[12px] pr-2"> (税込)</span>
                            {% if Product.stock_find == 0 %}
                                <span class="inline-block text-[11px] md:text-[12px] text-[#FF0000] ">
                                    Sold out
                                </span>
                            {% endif %}
                        </p>
                    {% else %}
                        <p class="pt-2 text-[14px] md:text-[18px]">
                            {% if Product.hasProductClass -%}
                                {% if Product.getPrice02IncTaxMin == Product.getPrice02IncTaxMax %}
                                    {{ Product.getPrice02IncTaxMin|number_format(0, '.', ',') }}
                                {% else %}
                                    {{ Product.getPrice02IncTaxMin|number_format(0, '.', ',') }}
                                    <span class="text-[12px] md:text-[14px]">円 ～</span>
                                    {{ Product.getPrice02IncTaxMax|number_format(0, '.', ',') }}
                                {% endif %}
                            {% else %}
                                {{Product.getPrice02IncTaxMin|number_format(0, '.', ',') }}
                            {% endif %}
                            <span class="text-[12px] md:text-[14px]">円</span>
                            <span class="text-[10px] md:text-[12px] pr-2"> (税込)</span>
                            {% if Product.stock_find == 0 %}
                                <span class="inline-block text-[11px] md:text-[12px] text-[#FF0000] ">
                                    Sold out
                                </span>
                            {% endif %}
                        </p>
                    {% endif %}
                </a>
            </li>
        {% endfor %}
        </ul>
    </div>
</div>